#!/usr/bin/env python3

import rospy
import json
import os
import math
from nav_msgs.msg import Odometry
from std_msgs.msg import Float64
from tf.transformations import euler_from_quaternion
from geometry_msgs.msg import Vector3

class BasketCalculator:
    def __init__(self):
        rospy.init_node('basket_calculator', anonymous=True)
        
        # 当前位置和姿态
        self.current_pose = None
        self.current_orientation = None
        self.basket_points = []
        self.selected_basket_id = 0
        self.y_angle = None
        self.current_angle = None
        self.fire_angle = None
        self.Ncurrent_angle = None
        
        # ROS订阅器
        self.odom_sub = rospy.Subscriber('/Odometry', Odometry, self.odom_callback)
        self.angle_sub  = rospy.Subscriber('/y_angle', Float64, self.angle_callback)
        self.angle_sub  = rospy.Subscriber('/current_angle', Float64, self.current_angle_callback)
        self.angle_sub  = rospy.Subscriber('/fire_angle', Float64, self.fire_angle_callback)
        self.angle_sub = rospy.Subscriber('/Ncurrent_angle', Float64, self.Ncurrent_angle_callback)

        print("=== 篮筐机器人位置显示器 ===")
        print("功能：显示篮筐位置和雷达位置")

    def angle_callback(self, msg):
        self.y_angle = msg.data
    def current_angle_callback(self, msg):
        self.current_angle = msg.data
    def fire_angle_callback(self, msg):
        self.fire_angle = msg.data
    def Ncurrent_angle_callback(self, msg):
        self.Ncurrent_angle = msg.data

    def odom_callback(self, msg):
        """处理里程计数据"""
        self.current_pose = msg.pose.pose.position
        self.current_orientation = msg.pose.pose.orientation
        
        # 打印位置信息
        if self.basket_points and self.selected_basket_id < len(self.basket_points):
            self.print_positions()

    def print_positions(self):
        """打印篮筐位置和雷达位置"""
        if self.current_pose is None or not self.basket_points:
            return
        
        # 获取目标篮筐位置
        basket = self.basket_points[self.selected_basket_id]
        
        # 打印位置信息（每秒一次）
        if hasattr(self, 'last_print_time'):
            if rospy.Time.now().to_sec() - self.last_print_time > 1.0:
                print(f"\n=== 位置信息 ===")
                print(f"雷达位置: ({self.current_pose.x:.3f}, {self.current_pose.y:.3f}, {self.current_pose.z:.3f})")
                print(f"篮筐位置: ({basket['x']:.3f}, {basket['y']:.3f}, {basket['z']:.3f})")
                
                
                # 计算距离
                dx = basket['x'] - self.current_pose.x
                dy = basket['y'] - self.current_pose.y
                dz = basket['z'] - self.current_pose.z
                distance_2d = math.sqrt(dx**2 + dy**2)
                
                print(f"2D距离: {distance_2d:.3f}m")
                print(f"y_angle: {self.y_angle:.3f}度")
                if self.current_angle is not None:
                    print(f"current_angle: {self.current_angle:.3f}度")
                else:
                    # 如果仍然是None，可以打印一条提示信息，方便调试
                    print("current_angle: 尚未接收到数据")
                    
                if self.Ncurrent_angle is not None:
                    print(f"Ncurrent_angle: {self.Ncurrent_angle:.3f}度")
                else:
                    # 如果仍然是None，可以打印一条提示信息，方便调试
                    print("Ncurrent_angle: 尚未接收到数据")

                if self.fire_angle is not None:
                    print(f"fire_angle: {self.fire_angle:.3f}度")
                else:
                    # 如果仍然是None，可以打印一条提示信息，方便调试
                    print("fire_angle: 尚未接收到数据")
                
                
                self.last_print_time = rospy.Time.now().to_sec()
        else:
            self.last_print_time = rospy.Time.now().to_sec()

    def load_basket_points(self, points_file):
        """加载篮筐标记点"""
        try:
            with open(points_file, 'r', encoding='utf-8') as f:
                points = json.load(f)
            
            print(f"✓ 加载了 {len(points)} 个篮筐标记点")
            return points
        except Exception as e:
            print(f"❌ 加载篮筐标记点失败: {e}")
            return []

    def select_basket(self):
        """选择目标篮筐"""
        if not self.basket_points:
            print("❌ 没有可用的篮筐")
            return
        
        print(f"\n=== 选择目标篮筐 ===")
        print("可用的篮筐:")
        for i, point in enumerate(self.basket_points):
            desc = point.get('description', f'篮筐_{i}')
            print(f"  {i}: {desc} ({point['x']:.3f}, {point['y']:.3f}, {point['z']:.3f})")
        
        try:
            choice = input(f"请选择目标篮筐ID (0-{len(self.basket_points)-1}, 默认0): ").strip()
            if choice == '':
                self.selected_basket_id = 0
            else:
                basket_id = int(choice)
                if 0 <= basket_id < len(self.basket_points):
                    self.selected_basket_id = basket_id
                else:
                    print(f"❌ 无效的篮筐ID，使用默认篮筐0")
                    self.selected_basket_id = 0
        except ValueError:
            print("❌ 输入格式错误，使用默认篮筐0")
            self.selected_basket_id = 0
        
        basket = self.basket_points[self.selected_basket_id]
        desc = basket.get('description', f'篮筐_{self.selected_basket_id}')
        print(f"✓ 选择了篮筐: {desc}")

    def run(self):
        """运行程序"""
        # 查找最新的标记点文件
        points_dir = os.path.expanduser("~/r2_2/src/faster-lio/marked_points")
        if not os.path.exists(points_dir):
            print(f"❌ 标记点目录不存在: {points_dir}")
            return
        
        json_files = [f for f in os.listdir(points_dir) if f.endswith('.json')]
        if not json_files:
            print("❌ 没有找到标记点文件")
            return
        
        json_files.sort(reverse=True)
        points_file = os.path.join(points_dir, json_files[0])
        print(f"使用标记点文件: {points_file}")
        
        # 加载篮筐标记点
        self.basket_points = self.load_basket_points(points_file)
        if not self.basket_points:
            return
        
        # 选择目标篮筐
        self.select_basket()
        
        print(f"\n✓ 位置显示器已启动")
        print("等待机器人位置数据...")
        
        # 保持运行
        rospy.spin()

def main():
    try:
        calculator = BasketCalculator()
        calculator.run()
    except rospy.ROSInterruptException:
        pass
    except KeyboardInterrupt:
        print("\n👋 位置显示器关闭")

if __name__ == '__main__':
    main()
